/**
 * Image Detail Screen (Screen 2)
 * Displays full-size image with caption
 */

import React, { useState } from 'react';
import {
  View,
  Image,
  Text,
  StyleSheet,
  ScrollView,
  useColorScheme,
  ActivityIndicator,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { useRoute, useNavigation, RouteProp } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { RootStackParamList } from '../types/GalleryTypes';

type ImageDetailRouteProp = RouteProp<RootStackParamList, 'ImageDetail'>;
type ImageDetailNavigationProp = NativeStackNavigationProp<RootStackParamList, 'ImageDetail'>;

const ImageDetailScreen: React.FC = () => {
  const route = useRoute<ImageDetailRouteProp>();
  const navigation = useNavigation<ImageDetailNavigationProp>();
  const isDarkMode = useColorScheme() === 'dark';
  const [isLoading, setIsLoading] = useState(true);
  const [imageError, setImageError] = useState(false);

  const { imageUrl, caption } = route.params;

  const handleImageLoad = () => {
    setIsLoading(false);
  };

  const handleImageError = () => {
    setIsLoading(false);
    setImageError(true);
  };

  const handleRetry = () => {
    setImageError(false);
    setIsLoading(true);
  };

  const renderImageContent = () => {
    if (imageError) {
      return (
        <View style={styles.errorContainer}>
          <Text style={[styles.errorText, isDarkMode && styles.errorTextDark]}>
            Failed to load image
          </Text>
          <TouchableOpacity style={styles.retryButton} onPress={handleRetry}>
            <Text style={styles.retryButtonText}>Retry</Text>
          </TouchableOpacity>
        </View>
      );
    }

    return (
      <>
        {isLoading && (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={isDarkMode ? '#ffffff' : '#007bff'} />
            <Text style={[styles.loadingText, isDarkMode && styles.loadingTextDark]}>
              Loading image...
            </Text>
          </View>
        )}
        <Image
          source={{ uri: imageUrl }}
          style={styles.fullImage}
          resizeMode="contain"
          onLoad={handleImageLoad}
          onError={handleImageError}
        />
      </>
    );
  };

  return (
    <ScrollView
      style={[styles.container, isDarkMode && styles.containerDark]}
      contentContainerStyle={styles.contentContainer}
      showsVerticalScrollIndicator={false}
    >
      <View style={styles.imageContainer}>
        {renderImageContent()}
      </View>
      
      <View style={[styles.captionContainer, isDarkMode && styles.captionContainerDark]}>
        <Text style={[styles.captionTitle, isDarkMode && styles.captionTitleDark]}>
          {caption.split(' - ')[0]}
        </Text>
        {caption.includes(' - ') && (
          <Text style={[styles.captionDescription, isDarkMode && styles.captionDescriptionDark]}>
            {caption.split(' - ')[1]}
          </Text>
        )}
      </View>

      <TouchableOpacity
        style={[styles.backButton, isDarkMode && styles.backButtonDark]}
        onPress={() => navigation.goBack()}
      >
        <Text style={[styles.backButtonText, isDarkMode && styles.backButtonTextDark]}>
          ← Back to Gallery
        </Text>
      </TouchableOpacity>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  containerDark: {
    backgroundColor: '#1a1a1a',
  },
  contentContainer: {
    flexGrow: 1,
  },
  imageContainer: {
    flex: 1,
    minHeight: 400,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f8f9fa',
    margin: 16,
    borderRadius: 12,
    overflow: 'hidden',
  },
  fullImage: {
    width: '100%',
    height: '100%',
    minHeight: 400,
  },
  loadingContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(248, 249, 250, 0.8)',
    zIndex: 1,
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
    color: '#6c757d',
  },
  loadingTextDark: {
    color: '#ffffff',
  },
  errorContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
  },
  errorText: {
    fontSize: 16,
    color: '#dc3545',
    marginBottom: 16,
    textAlign: 'center',
  },
  errorTextDark: {
    color: '#ff6b6b',
  },
  retryButton: {
    backgroundColor: '#007bff',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 8,
  },
  retryButtonText: {
    color: '#ffffff',
    fontSize: 14,
    fontWeight: '600',
  },
  captionContainer: {
    margin: 16,
    padding: 20,
    backgroundColor: '#f8f9fa',
    borderRadius: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
  },
  captionContainerDark: {
    backgroundColor: '#2d2d2d',
  },
  captionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#212529',
    marginBottom: 8,
  },
  captionTitleDark: {
    color: '#ffffff',
  },
  captionDescription: {
    fontSize: 16,
    color: '#6c757d',
    lineHeight: 24,
  },
  captionDescriptionDark: {
    color: '#adb5bd',
  },
  backButton: {
    margin: 16,
    marginTop: 8,
    backgroundColor: '#007bff',
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 8,
    alignItems: 'center',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.2,
    shadowRadius: 3.84,
  },
  backButtonDark: {
    backgroundColor: '#0d6efd',
  },
  backButtonText: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: '600',
  },
  backButtonTextDark: {
    color: '#ffffff',
  },
});

export default ImageDetailScreen;
