/**
 * Gallery Screen (Screen 1)
 * Displays a grid of image thumbnails using FlatList
 */

import React from 'react';
import {
  View,
  FlatList,
  Image,
  TouchableOpacity,
  StyleSheet,
  Text,
  useColorScheme,
  Dimensions,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { GalleryImage, RootStackParamList } from '../types/GalleryTypes';
import { GALLERY_IMAGES } from '../data/galleryData';

type GalleryScreenNavigationProp = StackNavigationProp<RootStackParamList, 'Gallery'>;

const { width } = Dimensions.get('window');
const ITEM_SIZE = (width - 48) / 2; // 2 columns with padding

const GalleryScreen: React.FC = () => {
  const navigation = useNavigation<GalleryScreenNavigationProp>();
  const isDarkMode = useColorScheme() === 'dark';

  const handleImagePress = (image: GalleryImage) => {
    navigation.navigate('ImageDetail', {
      imageUrl: image.url,
      caption: image.caption,
    });
  };

  const renderImageItem = ({ item }: { item: GalleryImage }) => (
    <TouchableOpacity
      style={[styles.imageContainer, isDarkMode && styles.imageContainerDark]}
      onPress={() => handleImagePress(item)}
      activeOpacity={0.8}
    >
      <Image
        source={{ uri: item.url }}
        style={styles.thumbnailImage}
        resizeMode="cover"
      />
      <View style={[styles.captionOverlay, isDarkMode && styles.captionOverlayDark]}>
        <Text
          style={[styles.captionText, isDarkMode && styles.captionTextDark]}
          numberOfLines={2}
          ellipsizeMode="tail"
        >
          {item.caption.split(' - ')[0]} {/* Show only the title part */}
        </Text>
      </View>
    </TouchableOpacity>
  );

  return (
    <View style={[styles.container, isDarkMode && styles.containerDark]}>
      <FlatList
        data={GALLERY_IMAGES}
        renderItem={renderImageItem}
        keyExtractor={(item) => item.id}
        numColumns={2}
        contentContainerStyle={styles.listContainer}
        columnWrapperStyle={styles.row}
        showsVerticalScrollIndicator={false}
        ItemSeparatorComponent={() => <View style={styles.separator} />}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  containerDark: {
    backgroundColor: '#1a1a1a',
  },
  listContainer: {
    padding: 16,
  },
  row: {
    justifyContent: 'space-between',
  },
  imageContainer: {
    width: ITEM_SIZE,
    height: ITEM_SIZE,
    borderRadius: 12,
    overflow: 'hidden',
    backgroundColor: '#f8f9fa',
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    marginBottom: 16,
  },
  imageContainerDark: {
    backgroundColor: '#2d2d2d',
  },
  thumbnailImage: {
    width: '100%',
    height: '70%',
  },
  captionOverlay: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    padding: 8,
    height: '30%',
    justifyContent: 'center',
  },
  captionOverlayDark: {
    backgroundColor: 'rgba(45, 45, 45, 0.95)',
  },
  captionText: {
    fontSize: 12,
    fontWeight: '600',
    color: '#212529',
    textAlign: 'center',
  },
  captionTextDark: {
    color: '#ffffff',
  },
  separator: {
    height: 8,
  },
});

export default GalleryScreen;
