/**
 * Posts List App
 * A React Native app that displays a scrollable list of posts with persistence
 *
 * @format
 */

import React, {useState, useEffect} from 'react';
import {
  SafeAreaView,
  StatusBar,
  StyleSheet,
  Text,
  View,
  FlatList,
  TouchableOpacity,
  Alert,
  useColorScheme,
} from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Define the Post interface
interface Post {
  id: string;
  title: string;
  body: string;
}

// Static predefined posts data
const INITIAL_POSTS: Post[] = [
  {
    id: '1',
    title: 'Welcome to React Native',
    body: 'React Native combines the best parts of native development with React, a best-in-class JavaScript library for building user interfaces.',
  },
  {
    id: '2',
    title: 'Learn Once, Write Anywhere',
    body: 'We don\'t make assumptions about the rest of your technology stack, so you can develop new features in React Native without rewriting existing code.',
  },
  {
    id: '3',
    title: 'Native Development For Everyone',
    body: 'React Native lets you build mobile apps using only JavaScript. It uses the same design as <PERSON>act, letting you compose a rich mobile UI using declarative components.',
  },
  {
    id: '4',
    title: 'Seamless Cross-Platform',
    body: 'React Native apps are real mobile apps. With React Native, you don\'t build a mobile web app, an HTML5 app, or a hybrid app. You build a real mobile app.',
  },
  {
    id: '5',
    title: 'Fast Refresh',
    body: 'See your changes as soon as you save. With the power of JavaScript, React Native lets you iterate at lightning speed.',
  },
  {
    id: '6',
    title: 'Hot Reloading',
    body: 'Make tweaks to your app and see the changes instantly. Hot reloading speeds up your development workflow.',
  },
  {
    id: '7',
    title: 'Component-Based Architecture',
    body: 'Build encapsulated components that manage their own state, then compose them to make complex UIs.',
  },
  {
    id: '8',
    title: 'Rich Ecosystem',
    body: 'Take advantage of the vast ecosystem of third-party libraries and tools available for React Native development.',
  },
];

const STORAGE_KEY = '@posts_list';

function App(): React.JSX.Element {
  const isDarkMode = useColorScheme() === 'dark';
  const [posts, setPosts] = useState<Post[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // Load posts from AsyncStorage on app start
  useEffect(() => {
    loadPosts();
  }, []);

  const loadPosts = async () => {
    try {
      const storedPosts = await AsyncStorage.getItem(STORAGE_KEY);
      if (storedPosts) {
        setPosts(JSON.parse(storedPosts));
      } else {
        // If no stored posts, use initial data and save it
        setPosts(INITIAL_POSTS);
        await AsyncStorage.setItem(STORAGE_KEY, JSON.stringify(INITIAL_POSTS));
      }
    } catch (error) {
      console.error('Error loading posts:', error);
      setPosts(INITIAL_POSTS);
    } finally {
      setIsLoading(false);
    }
  };

  const savePosts = async (newPosts: Post[]) => {
    try {
      await AsyncStorage.setItem(STORAGE_KEY, JSON.stringify(newPosts));
    } catch (error) {
      console.error('Error saving posts:', error);
    }
  };

  const deletePost = (postId: string) => {
    Alert.alert(
      'Delete Post',
      'Are you sure you want to delete this post?',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: () => {
            const updatedPosts = posts.filter(post => post.id !== postId);
            setPosts(updatedPosts);
            savePosts(updatedPosts);
          },
        },
      ],
    );
  };

  const refreshPosts = () => {
    Alert.alert(
      'Refresh Posts',
      'This will restore all original posts. Continue?',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Refresh',
          onPress: () => {
            setPosts(INITIAL_POSTS);
            savePosts(INITIAL_POSTS);
          },
        },
      ],
    );
  };

  const renderPost = ({item}: {item: Post}) => (
    <View style={[styles.postCard, isDarkMode && styles.postCardDark]}>
      <View style={styles.postContent}>
        <Text style={[styles.postTitle, isDarkMode && styles.textDark]}>
          {item.title}
        </Text>
        <Text style={[styles.postBody, isDarkMode && styles.textDark]}>
          {item.body}
        </Text>
      </View>
      <TouchableOpacity
        style={styles.deleteButton}
        onPress={() => deletePost(item.id)}
        accessibilityLabel={`Delete post: ${item.title}`}>
        <Text style={styles.deleteButtonText}>Delete</Text>
      </TouchableOpacity>
    </View>
  );

  const renderEmptyState = () => (
    <View style={styles.emptyState}>
      <Text style={[styles.emptyStateText, isDarkMode && styles.textDark]}>
        📝 No posts available
      </Text>
      <Text style={[styles.emptyStateSubtext, isDarkMode && styles.textDark]}>
        Tap the refresh button to restore original posts
      </Text>
    </View>
  );

  if (isLoading) {
    return (
      <SafeAreaView style={[styles.container, isDarkMode && styles.containerDark]}>
        <StatusBar
          barStyle={isDarkMode ? 'light-content' : 'dark-content'}
          backgroundColor={isDarkMode ? '#1a1a1a' : '#ffffff'}
        />
        <View style={styles.loadingContainer}>
          <Text style={[styles.loadingText, isDarkMode && styles.textDark]}>
            Loading posts...
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={[styles.container, isDarkMode && styles.containerDark]}>
      <StatusBar
        barStyle={isDarkMode ? 'light-content' : 'dark-content'}
        backgroundColor={isDarkMode ? '#1a1a1a' : '#ffffff'}
      />

      {/* Header */}
      <View style={[styles.header, isDarkMode && styles.headerDark]}>
        <Text style={[styles.headerTitle, isDarkMode && styles.textDark]}>
          My Posts
        </Text>
        <TouchableOpacity
          style={[styles.refreshButton, isDarkMode && styles.refreshButtonDark]}
          onPress={refreshPosts}
          accessibilityLabel="Refresh posts">
          <Text style={[styles.refreshButtonText, isDarkMode && styles.refreshButtonTextDark]}>
            🔄 Refresh
          </Text>
        </TouchableOpacity>
      </View>

      {/* Posts List */}
      <FlatList
        data={posts}
        renderItem={renderPost}
        keyExtractor={item => item.id}
        contentContainerStyle={[
          styles.listContainer,
          posts.length === 0 && styles.listContainerEmpty,
        ]}
        ListEmptyComponent={renderEmptyState}
        showsVerticalScrollIndicator={false}
        ItemSeparatorComponent={() => <View style={styles.separator} />}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  containerDark: {
    backgroundColor: '#1a1a1a',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: '#f8f9fa',
    borderBottomWidth: 1,
    borderBottomColor: '#e9ecef',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
  },
  headerDark: {
    backgroundColor: '#2d2d2d',
    borderBottomColor: '#404040',
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#212529',
  },
  refreshButton: {
    backgroundColor: '#007bff',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.2,
    shadowRadius: 3.84,
  },
  refreshButtonDark: {
    backgroundColor: '#0d6efd',
  },
  refreshButtonText: {
    color: '#ffffff',
    fontSize: 14,
    fontWeight: '600',
  },
  refreshButtonTextDark: {
    color: '#ffffff',
  },
  listContainer: {
    padding: 16,
  },
  listContainerEmpty: {
    flex: 1,
    justifyContent: 'center',
  },
  postCard: {
    backgroundColor: '#ffffff',
    borderRadius: 12,
    padding: 16,
    marginVertical: 4,
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    borderWidth: 1,
    borderColor: '#e9ecef',
  },
  postCardDark: {
    backgroundColor: '#2d2d2d',
    borderColor: '#404040',
  },
  postContent: {
    flex: 1,
    marginBottom: 12,
  },
  postTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#212529',
    marginBottom: 8,
    lineHeight: 24,
  },
  postBody: {
    fontSize: 14,
    color: '#6c757d',
    lineHeight: 20,
  },
  deleteButton: {
    backgroundColor: '#dc3545',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
    alignSelf: 'flex-end',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.2,
    shadowRadius: 3.84,
  },
  deleteButtonText: {
    color: '#ffffff',
    fontSize: 12,
    fontWeight: '600',
  },
  separator: {
    height: 8,
  },
  emptyState: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 60,
  },
  emptyStateText: {
    fontSize: 20,
    fontWeight: '600',
    color: '#6c757d',
    marginBottom: 8,
    textAlign: 'center',
  },
  emptyStateSubtext: {
    fontSize: 14,
    color: '#adb5bd',
    textAlign: 'center',
    paddingHorizontal: 40,
  },
  textDark: {
    color: '#ffffff',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: '#6c757d',
  },
});

export default App;
