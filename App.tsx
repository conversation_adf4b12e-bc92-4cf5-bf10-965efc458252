/**
 * Gallery App
 * A React Native app that displays a gallery of images with navigation
 *
 * @format
 */

import React from 'react';
import { StatusBar, useColorScheme } from 'react-native';
import { NavigationContainer } from '@react-navigation/native';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { RootStackParamList } from './src/types/GalleryTypes';
import MainMenuScreen from './src/screens/MainMenuScreen';
import PostsListScreen from './src/screens/PostsListScreen';
import GalleryScreen from './src/screens/GalleryScreen';
import ImageDetailScreen from './src/screens/ImageDetailScreen';

const Stack = createNativeStackNavigator<RootStackParamList>();

function App(): React.JSX.Element {
  const isDarkMode = useColorScheme() === 'dark';

  return (
    <NavigationContainer>
      <StatusBar
        barStyle={isDarkMode ? 'light-content' : 'dark-content'}
        backgroundColor={isDarkMode ? '#1a1a1a' : '#ffffff'}
      />
      <Stack.Navigator
        initialRouteName="MainMenu"
        screenOptions={{
          headerStyle: {
            backgroundColor: isDarkMode ? '#2d2d2d' : '#f8f9fa',
          },
          headerTintColor: isDarkMode ? '#ffffff' : '#212529',
          headerTitleStyle: {
            fontWeight: 'bold',
            fontSize: 18,
          },
          contentStyle: {
            backgroundColor: isDarkMode ? '#1a1a1a' : '#ffffff',
          },
        }}
      >
        <Stack.Screen
          name="MainMenu"
          component={MainMenuScreen}
          options={{
            title: 'React Native Apps',
            headerTitleAlign: 'center',
          }}
        />
        <Stack.Screen
          name="PostsList"
          component={PostsListScreen}
          options={{
            title: '📝 My Posts',
            headerTitleAlign: 'center',
          }}
        />
        <Stack.Screen
          name="Gallery"
          component={GalleryScreen}
          options={{
            title: '📸 Photo Gallery',
            headerTitleAlign: 'center',
          }}
        />
        <Stack.Screen
          name="ImageDetail"
          component={ImageDetailScreen}
          options={{
            title: 'Photo Details',
            headerTitleAlign: 'center',
            headerBackTitleVisible: false,
          }}
        />
      </Stack.Navigator>
    </NavigationContainer>
  );
}

export default App;
