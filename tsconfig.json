{"compilerOptions": {"allowJs": true, "allowSyntheticDefaultImports": true, "esModuleInterop": true, "isolatedModules": true, "jsx": "react-native", "lib": ["es2017"], "moduleResolution": "node", "noEmit": true, "strict": true, "target": "esnext", "resolveJsonModule": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "baseUrl": ".", "paths": {"*": ["node_modules/*"]}}, "include": ["src/**/*", "App.tsx", "index.js"], "exclude": ["node_modules", "babel.config.js", "metro.config.js", "jest.config.js", "__tests__", "android", "ios"]}