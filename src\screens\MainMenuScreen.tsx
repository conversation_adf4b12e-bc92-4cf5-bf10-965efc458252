/**
 * Main Menu Screen
 * A screen that allows users to choose between different apps
 */

import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  SafeAreaView,
  useColorScheme,
  StatusBar,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { RootStackParamList } from '../types/GalleryTypes';

type MainMenuNavigationProp = NativeStackNavigationProp<RootStackParamList, 'MainMenu'>;

const MainMenuScreen: React.FC = () => {
  const navigation = useNavigation<MainMenuNavigationProp>();
  const isDarkMode = useColorScheme() === 'dark';

  const navigateToPostsList = () => {
    navigation.navigate('PostsList');
  };

  const navigateToGallery = () => {
    navigation.navigate('Gallery');
  };

  return (
    <SafeAreaView style={[styles.container, isDarkMode && styles.containerDark]}>
      <StatusBar
        barStyle={isDarkMode ? 'light-content' : 'dark-content'}
        backgroundColor={isDarkMode ? '#1a1a1a' : '#ffffff'}
      />
      
      <View style={styles.content}>
        <View style={styles.headerSection}>
          <Text style={[styles.title, isDarkMode && styles.titleDark]}>
            📱 React Native Apps
          </Text>
          <Text style={[styles.subtitle, isDarkMode && styles.subtitleDark]}>
            Choose an app to explore
          </Text>
        </View>

        <View style={styles.menuSection}>
          <TouchableOpacity
            style={[styles.menuButton, styles.postsButton, isDarkMode && styles.menuButtonDark]}
            onPress={navigateToPostsList}
            activeOpacity={0.8}
          >
            <View style={styles.buttonContent}>
              <Text style={styles.buttonIcon}>📝</Text>
              <Text style={[styles.buttonTitle, isDarkMode && styles.buttonTitleDark]}>
                Posts List App
              </Text>
              <Text style={[styles.buttonDescription, isDarkMode && styles.buttonDescriptionDark]}>
                View, manage, and persist posts with AsyncStorage
              </Text>
            </View>
            <Text style={[styles.buttonArrow, isDarkMode && styles.buttonArrowDark]}>→</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.menuButton, styles.galleryButton, isDarkMode && styles.menuButtonDark]}
            onPress={navigateToGallery}
            activeOpacity={0.8}
          >
            <View style={styles.buttonContent}>
              <Text style={styles.buttonIcon}>📸</Text>
              <Text style={[styles.buttonTitle, isDarkMode && styles.buttonTitleDark]}>
                Photo Gallery App
              </Text>
              <Text style={[styles.buttonDescription, isDarkMode && styles.buttonDescriptionDark]}>
                Browse beautiful images in a grid layout
              </Text>
            </View>
            <Text style={[styles.buttonArrow, isDarkMode && styles.buttonArrowDark]}>→</Text>
          </TouchableOpacity>
        </View>

        <View style={styles.footerSection}>
          <Text style={[styles.footerText, isDarkMode && styles.footerTextDark]}>
            Built with React Native & TypeScript
          </Text>
        </View>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  containerDark: {
    backgroundColor: '#1a1a1a',
  },
  content: {
    flex: 1,
    padding: 20,
    justifyContent: 'space-between',
  },
  headerSection: {
    alignItems: 'center',
    marginTop: 40,
    marginBottom: 40,
  },
  title: {
    fontSize: 32,
    fontWeight: 'bold',
    color: '#212529',
    marginBottom: 8,
    textAlign: 'center',
  },
  titleDark: {
    color: '#ffffff',
  },
  subtitle: {
    fontSize: 16,
    color: '#6c757d',
    textAlign: 'center',
  },
  subtitleDark: {
    color: '#adb5bd',
  },
  menuSection: {
    flex: 1,
    justifyContent: 'center',
    gap: 20,
  },
  menuButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f8f9fa',
    borderRadius: 16,
    padding: 20,
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    borderWidth: 1,
    borderColor: '#e9ecef',
  },
  menuButtonDark: {
    backgroundColor: '#2d2d2d',
    borderColor: '#404040',
  },
  postsButton: {
    borderLeftWidth: 4,
    borderLeftColor: '#007bff',
  },
  galleryButton: {
    borderLeftWidth: 4,
    borderLeftColor: '#28a745',
  },
  buttonContent: {
    flex: 1,
  },
  buttonIcon: {
    fontSize: 32,
    marginBottom: 8,
  },
  buttonTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#212529',
    marginBottom: 4,
  },
  buttonTitleDark: {
    color: '#ffffff',
  },
  buttonDescription: {
    fontSize: 14,
    color: '#6c757d',
    lineHeight: 20,
  },
  buttonDescriptionDark: {
    color: '#adb5bd',
  },
  buttonArrow: {
    fontSize: 24,
    color: '#007bff',
    fontWeight: 'bold',
  },
  buttonArrowDark: {
    color: '#0d6efd',
  },
  footerSection: {
    alignItems: 'center',
    marginTop: 40,
  },
  footerText: {
    fontSize: 12,
    color: '#adb5bd',
    textAlign: 'center',
  },
  footerTextDark: {
    color: '#6c757d',
  },
});

export default MainMenuScreen;
